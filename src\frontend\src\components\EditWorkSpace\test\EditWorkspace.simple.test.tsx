import { render, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { describe, it, expect, vi } from 'vitest';
import EditWorkspace from '../EditWorkspace';
import workspaceReducer from '../../../features/workspaceSlice';

// Mock react-router hooks
vi.mock('react-router', () => ({
  useNavigate: () => vi.fn(),
  useLocation: () => ({ search: '' }),
}));

// Mock external dependencies
vi.mock('../../../hooks/useAuth', () => ({
  useAuth: () => ({
    acquireToken: vi.fn().mockResolvedValue({ accessToken: 'mock-token' }),
    activeAccount: { username: '<EMAIL>' },
    getAuthResult: vi.fn(),
  }),
}));

vi.mock('../../../services/workspacesService', () => ({
  fetchWorkspaceById: vi.fn(),
  createWorkspace: vi.fn(),
  editWorkspace: vi.fn(),
}));

vi.mock('../../../services/documentsService', () => ({
  uploadDocument: vi.fn(),
}));

vi.mock('../../../utils/processResponseStream', () => ({
  processResponseStream: vi.fn(),
}));

vi.mock('react-dropzone', () => ({
  useDropzone: vi.fn(() => ({
    getRootProps: () => ({ 'data-testid': 'dropzone' }),
    getInputProps: () => ({ 'data-testid': 'file-input' }),
    isDragActive: false,
  })),
}));

// Mock child components
vi.mock('../BottomButtons', () => ({
  default: ({ save, workspaceIdFromUrl }: any) => (
    <div data-testid="bottom-buttons">
      <button onClick={save} data-testid="save-button">
        {workspaceIdFromUrl ? 'Update' : 'Create'}
      </button>
    </div>
  ),
}));

vi.mock('../InputSection', () => ({
  default: ({ name, description }: any) => (
    <div data-testid="input-section">
      <input data-testid="workspace-name" value={name} readOnly />
      <textarea data-testid="workspace-description" value={description} readOnly />
    </div>
  ),
}));

vi.mock('../../DocsContainer/DocumentsList', () => ({
  default: () => <div data-testid="documents-list">Documents List</div>,
}));

vi.mock('../../Tooltip/Tooltip', () => ({
  default: ({ children }: any) => <div data-testid="tooltip">{children}</div>,
}));

vi.mock('../../Modal/ToastModal/Toast', () => ({
  default: ({ text }: any) => <div data-testid="toast">{text}</div>,
}));

describe('EditWorkspace - Simple Tests', () => {
  const createMockStore = () => {
    return configureStore({
      reducer: {
        workspace: workspaceReducer,
      },
      preloadedState: {
        workspace: {
          id: '',
          name: '',
          description: '',
          documents: [],
        },
      },
    });
  };

  const renderComponent = () => {
    const mockStore = createMockStore();
    return render(
      <Provider store={mockStore}>
        <MemoryRouter>
          <EditWorkspace />
        </MemoryRouter>
      </Provider>
    );
  };

  it('should render the main heading', () => {
    renderComponent();
    expect(screen.getByText('Document Workspaces')).toBeInTheDocument();
  });

  it('should render the Quick Start guide link', () => {
    renderComponent();
    const quickStartLink = screen.getByText('Quick Start guide');
    expect(quickStartLink).toBeInTheDocument();
    expect(quickStartLink).toHaveAttribute('target', '_blank');
  });

  it('should render InputSection and BottomButtons components', () => {
    renderComponent();
    expect(screen.getByTestId('input-section')).toBeInTheDocument();
    expect(screen.getByTestId('bottom-buttons')).toBeInTheDocument();
  });

  it('should show Create button for new workspace', () => {
    renderComponent();
    expect(screen.getByText('Create')).toBeInTheDocument();
  });

  it('should apply correct CSS classes for theming', () => {
    renderComponent();
    const container = screen.getByText('Document Workspaces').closest('div');
    expect(container).toHaveClass('bg-white', 'dark:bg-zinc-800');
  });
});
