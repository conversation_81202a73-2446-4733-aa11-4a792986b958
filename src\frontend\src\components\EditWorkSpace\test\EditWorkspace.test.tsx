import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import EditWorkspace from '../EditWorkspace';
import { Workspace, DocumentRecord } from '../../../interfaces';
import workspaceReducer from '../../../features/workspaceSlice';
import userEvent from '@testing-library/user-event';

// Mock react-router hooks
const mockNavigate = vi.fn();
const mockLocation = { search: '' };

vi.mock('react-router', () => ({
  useNavigate: () => mockNavigate,
  useLocation: () => mockLocation,
}));

// Mock external dependencies
const mockAcquireToken = vi.fn().mockResolvedValue({ accessToken: 'mock-token' });
const mockActiveAccount = { username: '<EMAIL>' };

vi.mock('../../../hooks/useAuth', () => ({
  useAuth: () => ({
    acquireToken: mockAcquireToken,
    activeAccount: mockActiveAccount,
    getAuthResult: vi.fn(),
  }),
}));

vi.mock('../../../services/workspacesService', () => ({
  fetchWorkspaceById: vi.fn(),
  createWorkspace: vi.fn(),
  editWorkspace: vi.fn(),
}));

vi.mock('../../../services/documentsService', () => ({
  uploadDocument: vi.fn(),
}));

vi.mock('../../../utils/processResponseStream', () => ({
  processResponseStream: vi.fn(),
}));

vi.mock('react-dropzone', () => ({
  useDropzone: vi.fn(() => ({
    getRootProps: () => ({ 'data-testid': 'dropzone' }),
    getInputProps: () => ({ 'data-testid': 'file-input' }),
    isDragActive: false,
  })),
}));

// Mock child components
vi.mock('../BottomButtons', () => ({
  default: ({ save, workspaceIdFromUrl, activeAccount }: any) => (
    <div data-testid="bottom-buttons">
      <button onClick={save} data-testid="save-button">
        {workspaceIdFromUrl ? 'Update' : 'Create'}
      </button>
    </div>
  ),
}));

vi.mock('../InputSection', () => ({
  default: ({ name, description, inputChange }: any) => (
    <div data-testid="input-section">
      <input
        data-testid="workspace-name"
        value={name}
        onChange={(e) => inputChange({ target: { name: 'name', value: e.target.value } })}
      />
      <textarea
        data-testid="workspace-description"
        value={description}
        onChange={(e) => inputChange({ target: { name: 'description', value: e.target.value } })}
      />
    </div>
  ),
}));

vi.mock('../../DocsContainer/DocumentsList', () => ({
  default: () => <div data-testid="documents-list">Documents List</div>,
}));

vi.mock('../../Tooltip/Tooltip', () => ({
  default: ({ children, message }: any) => (
    <div title={message} data-testid="tooltip">
      {children}
    </div>
  ),
}));

vi.mock('../../Modal/ToastModal/Toast', () => ({
  default: ({ text, onClose, id }: any) => (
    <div data-testid={`toast-${id}`} onClick={onClose}>
      {text}
    </div>
  ),
}));

describe('EditWorkspace', () => {
  let mockStore: any;

  const createMockStore = (initialState: Partial<Workspace> = {}) => {
    return configureStore({
      reducer: {
        workspace: workspaceReducer,
      },
      preloadedState: {
        workspace: {
          id: '',
          name: '',
          description: '',
          documents: [],
          ...initialState,
        },
      },
    });
  };

  const renderComponent = (
    initialPath = '/edit-workspace',
    storeState: Partial<Workspace> = {}
  ) => {
    mockStore = createMockStore(storeState);
    return render(
      <Provider store={mockStore}>
        <MemoryRouter initialEntries={[initialPath]}>
          <EditWorkspace />
        </MemoryRouter>
      </Provider>
    );
  };

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    mockNavigate.mockClear();
    mockAcquireToken.mockClear();

    // Reset location mock
    mockLocation.search = '';
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Component Rendering', () => {
    it('should render the main heading', () => {
      renderComponent();
      expect(screen.getByText('Document Workspaces')).toBeInTheDocument();
    });

    it('should render the Quick Start guide link with correct attributes', () => {
      renderComponent();
      const quickStartLink = screen.getByText('Quick Start guide');
      
      expect(quickStartLink).toBeInTheDocument();
      expect(quickStartLink).toHaveAttribute('href', expect.stringContaining('sharepoint.com'));
      expect(quickStartLink).toHaveAttribute('target', '_blank');
      expect(quickStartLink).toHaveClass('text-gallagher-dark-300', 'dark:text-sky-400', 'underline');
    });

    it('should render InputSection and BottomButtons components', () => {
      renderComponent();
      expect(screen.getByTestId('input-section')).toBeInTheDocument();
      expect(screen.getByTestId('bottom-buttons')).toBeInTheDocument();
    });

    it('should apply dark theme classes correctly', () => {
      renderComponent();
      const container = screen.getByText('Document Workspaces').closest('div');
      expect(container).toHaveClass('dark:bg-zinc-800');
    });
  });

  describe('New Workspace Creation', () => {
    it('should not show documents section when creating new workspace', () => {
      renderComponent();
      expect(screen.queryByText('Documents uploaded:')).not.toBeInTheDocument();
      expect(screen.queryByTestId('documents-list')).not.toBeInTheDocument();
    });

    it('should show Create button for new workspace', () => {
      renderComponent();
      expect(screen.getByText('Create')).toBeInTheDocument();
    });
  });

  describe('Existing Workspace Editing', () => {
    it('should show documents section when editing existing workspace', () => {
      renderComponent('/edit-workspace?id=123');
      expect(screen.getByText('Documents uploaded:')).toBeInTheDocument();
      expect(screen.getByTestId('documents-list')).toBeInTheDocument();
    });

    it('should show Update button for existing workspace', () => {
      renderComponent('/edit-workspace?id=123');
      expect(screen.getByText('Update')).toBeInTheDocument();
    });
  });

  describe('Redux State Integration', () => {
    it('should display workspace name and description from Redux state', () => {
      const initialState = {
        name: 'Test Workspace',
        description: 'Test Description',
      };
      
      renderComponent('/edit-workspace', initialState);
      
      expect(screen.getByDisplayValue('Test Workspace')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Test Description')).toBeInTheDocument();
    });

    it('should handle input changes and update Redux state', async () => {
      const user = userEvent.setup();
      renderComponent();
      
      const nameInput = screen.getByTestId('workspace-name');
      await user.type(nameInput, 'New Workspace Name');
      
      expect(nameInput).toHaveValue('New Workspace Name');
    });
  });

  describe('File Upload Functionality', () => {
    it('should render dropzone when editing existing workspace', () => {
      renderComponent('/edit-workspace?id=123');
      expect(screen.getByTestId('dropzone')).toBeInTheDocument();
    });

    it('should not render dropzone when creating new workspace', () => {
      renderComponent();
      expect(screen.queryByTestId('dropzone')).not.toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should handle token acquisition failure gracefully', async () => {
      mockAcquireToken.mockResolvedValue(null);
      
      renderComponent();
      const saveButton = screen.getByTestId('save-button');
      
      await act(async () => {
        fireEvent.click(saveButton);
      });
      
      // Should not crash and should handle the error
      expect(mockAcquireToken).toHaveBeenCalled();
    });
  });

  describe('Navigation', () => {
    it('should navigate to manage-workspaces after creating new workspace', async () => {
      const createWorkspace = vi.mocked(await import('../../../services/workspacesService')).createWorkspace;
      createWorkspace.mockResolvedValue({} as any);

      renderComponent();
      const saveButton = screen.getByTestId('save-button');

      await act(async () => {
        fireEvent.click(saveButton);
      });

      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith('/manage-workspaces');
      });
    });
  });

  describe('Basic Functionality', () => {
    it('should show success toast when workspace is updated', async () => {
      const editWorkspace = vi.mocked(await import('../../../services/workspacesService')).editWorkspace;
      editWorkspace.mockResolvedValue({} as any);

      // Set location to have an ID for editing
      mockLocation.search = '?id=123';

      renderComponent('/edit-workspace?id=123', { name: 'Test', description: 'Test' });
      const saveButton = screen.getByTestId('save-button');

      await act(async () => {
        fireEvent.click(saveButton);
      });

      await waitFor(() => {
        expect(screen.getByText('Workspace information updated')).toBeInTheDocument();
      });
    });
  });

  describe('Document Management', () => {
    it('should display document limit tooltip when limit is reached', () => {
      const documents: DocumentRecord[] = Array.from({ length: 10 }, (_, i) => ({
        name: `doc${i}.pdf`,
        processed: 1 as const,
      }));

      renderComponent('/edit-workspace?id=123', { documents });

      expect(screen.queryByTestId('dropzone')).not.toBeInTheDocument();
    });

    it('should show dropzone when document limit is not reached', () => {
      const documents: DocumentRecord[] = [
        { name: 'doc1.pdf', processed: 1 as const },
      ];

      renderComponent('/edit-workspace?id=123', { documents });

      expect(screen.getByTestId('dropzone')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels and roles', () => {
      renderComponent();

      const quickStartLink = screen.getByText('Quick Start guide');
      expect(quickStartLink).toHaveAttribute('target', '_blank');

      // Check that the link opens in a new tab for accessibility
      expect(quickStartLink.closest('a')).toHaveAttribute('target', '_blank');
    });

    it('should handle keyboard navigation properly', async () => {
      const user = userEvent.setup();
      renderComponent();

      const nameInput = screen.getByTestId('workspace-name');

      await user.tab();
      expect(nameInput).toHaveFocus();
    });
  });

  describe('Theme Support', () => {
    it('should apply light theme classes by default', () => {
      renderComponent();

      const container = screen.getByText('Document Workspaces').closest('div');
      expect(container).toHaveClass('bg-white');
    });

    it('should support dark theme classes', () => {
      renderComponent();

      const container = screen.getByText('Document Workspaces').closest('div');
      expect(container).toHaveClass('dark:bg-zinc-800');

      const heading = screen.getByText('Document Workspaces');
      expect(heading).toHaveClass('dark:text-gray-300');
    });
  });

  describe('Component Lifecycle', () => {
    it('should cleanup controllers on unmount', () => {
      const { unmount } = renderComponent('/edit-workspace?id=123');

      // Component should mount without errors
      expect(screen.getByText('Document Workspaces')).toBeInTheDocument();

      // Should unmount without errors
      unmount();
    });

    it('should fetch workspace data when workspaceId is provided', async () => {
      const fetchWorkspaceById = vi.mocked(await import('../../../services/workspacesService')).fetchWorkspaceById;
      fetchWorkspaceById.mockResolvedValue(new Response());

      mockLocation.search = '?id=123';
      renderComponent('/edit-workspace?id=123');

      await waitFor(() => {
        expect(fetchWorkspaceById).toHaveBeenCalledWith('123', 'mock-token', expect.any(AbortSignal));
      });
    });

    it('should clear workspace state when creating new workspace', () => {
      renderComponent('/edit-workspace');

      // Should start with empty state for new workspace
      expect(screen.getByDisplayValue('')).toBeInTheDocument();
    });
  });
});
